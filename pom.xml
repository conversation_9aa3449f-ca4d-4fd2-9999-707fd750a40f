<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.example</groupId>
    <artifactId>EndMinecraftPlusPlus</artifactId>
    <version>1.15.1-SNAPSHOT</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.0.1.RELEASE</version>
                <configuration>
                    <maimClass>net.emtips.endminecraftplusplus.main.main</maimClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.26.0-GA</version>
        </dependency>
        <dependency>
            <groupId> mcprotocollib </groupId>
            <artifactId> mcprotocollib </artifactId>
            <version>1.15.1</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/Libs/mcprotocollib-1.15.1-1-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId> packetlib </groupId>
            <artifactId> packetlib </artifactId>
            <version>1.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/Libs/packetlib-1.5-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId> mcauthlib </groupId>
            <artifactId> mcauthlib </artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/Libs/mcauthlib-1.2-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.44.Final</version>
        </dependency>
    </dependencies>

    <repositories>
        <!-- This adds the Spigot Maven repository to the build -->
        <repository>
            <id>maven2</id>
            <url>https://maven.aliyun.com/repository/central</url>
        </repository>

    </repositories>

</project>