<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="Test" time="0.007" tests="0" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\target\test-classes;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\target\classes;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.26.0-GA\javassist-3.26.0-GA.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\mcprotocollib-1.15.1-1-SNAPSHOT.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\packetlib-1.5-SNAPSHOT.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\mcauthlib-1.2-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.44.Final\netty-all-4.1.44.Final.jar;"/>
    <property name="java.vm.vendor" value="Azul Systems, Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://www.azul.com/"/>
    <property name="user.timezone" value="GMT+08:00"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\Java\zulu8\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire4597550001478564715\surefirebooter-20250802211439362_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire4597550001478564715 2025-08-02T21-14-37_692-jvmRun1 surefire-20250802211439362_1tmp surefire_0-20250802211439362_2tmp"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\target\test-classes;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\target\classes;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.26.0-GA\javassist-3.26.0-GA.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\mcprotocollib-1.15.1-1-SNAPSHOT.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\packetlib-1.5-SNAPSHOT.jar;C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master\Libs\mcauthlib-1.2-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.44.Final\netty-all-4.1.44.Final.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\zulu8\jre"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="jdk.vendor.version" value="Zulu 8.86.0.25-CA-win64"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire4597550001478564715\surefirebooter-20250802211439362_3.jar"/>
    <property name="sun.boot.class.path" value="D:\Java\zulu8\jre\lib\resources.jar;D:\Java\zulu8\jre\lib\rt.jar;D:\Java\zulu8\jre\lib\sunrsasign.jar;D:\Java\zulu8\jre\lib\jsse.jar;D:\Java\zulu8\jre\lib\jce.jar;D:\Java\zulu8\jre\lib\charsets.jar;D:\Java\zulu8\jre\lib\jfr.jar;D:\Java\zulu8\jre\lib\cat.jar;D:\Java\zulu8\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_452-b09"/>
    <property name="user.name" value="9527"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\Java\zulu8\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://www.azul.com/support/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_452"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\MCDrink1.1-public version\EndMinecraftMax-master"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\Java\zulu8\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\Java\zulu17\bin\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\Java\jdk-1.8\bin;C:\Maven\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;D:\IDEA\IntelliJ IDEA Community Edition\bin;;D:\Microsoft VS Code\bin;D:\IDEA\Comate\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Azul Systems, Inc."/>
    <property name="java.vm.version" value="25.452-b09"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="D:\Java\zulu8\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
</testsuite>